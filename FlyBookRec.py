import time
from urllib.parse import urlencode

from websocket import WebSock<PERSON><PERSON><PERSON>
import static.proto_pb2 as FLY_BOOK_PROTO
from FlyBookApi import FlyBookApi

from builder.auth import FlyBookAuth
from builder.params import ParamsBuilder
from builder.proto import ProtoBuilder


class FlyBookLive:
    def __init__(self):
        self.ws = None

    @staticmethod
    def send_ack(ws, Packet_sid):
        payload = FLY_BOOK_PROTO.Packet()
        payload.cmd = 1
        payload.payloadType = 1
        payload.sid = Packet_sid
        payload = payload.SerializeToString()
        Frame = FLY_BOOK_PROTO.Frame()
        current = int(time.time() * 1000)
        Frame.seqid = current
        Frame.logid = current
        Frame.service = 1
        Frame.method = 1
        ExtendedEntry = FLY_BOOK_PROTO.ExtendedEntry()
        ExtendedEntry.key = 'x-request-time'
        ExtendedEntry.value = f'{current}000'
        Frame.headers.append(ExtendedEntry)
        Frame.payloadType = "pb"
        Frame.payload = payload
        Frame = Frame.SerializeToString()
        ws.send(Frame, opcode=0x2)

    @staticmethod
    def on_message(ws, message):
        try:
            Frame, Packet_sid, fromId, ChatId, ReceiveTextContent = ProtoBuilder.decode_receive_msg_proto(message)

            # 心跳ACK
            FlyBookLive.send_ack(ws, Packet_sid)
            print('==============================')
        except Exception as e:
            print(str(e))

    @staticmethod
    def on_error(ws, error):
        print("\033[31m### error ###")
        print(error)
        print("### ===error=== ###\033[m")

    @staticmethod
    def on_close(ws, close_status_code, close_msg):
        print("\033[31m### closed ###")
        print(f"status_code: {close_status_code}, msg: {close_msg}")
        print("### ===closed=== ###\033[m")

    def start_ws(self, wss_url):
        self.ws = WebSocketApp(
            url=wss_url,
            on_message=FlyBookLive.on_message,
            on_error=FlyBookLive.on_error,
            on_close=FlyBookLive.on_close
        )
        try:
            self.ws.run_forever()
        except Exception as e:
            print(str(e))
            self.ws.close()


if __name__ == '__main__':
    live = FlyBookLive()
    fly_book_auth = FlyBookAuth()
    cookie_str = 'passport_web_did=7360675712451543044; QXV0aHpDb250ZXh0=********************************; locale=zh-CN; _gcl_au=1.1.1773137480.1713790863; trust_browser_id=1078ea84-34d9-42d4-adff-766c423ab3e8; _ga=GA1.1.*********.1713790863; is_anonymous_session=; _ga_VPYRHN104D=GS1.1.1713790863.1.1.1713790872.51.0.0; lang=zh; __tea__ug__uid=1007701713790873353; _uuid_hera_ab_path_1=7372781885275160579; Hm_lvt_e78c0cb1b97ef970304b53d2097845fd=1716609558; _csrf_token=36b375aad3381c672c2752df4c2f78c9bc1b51ae-1716787751; session=XN0YXJ0-8f0hd84c-4929-4782-8aa7-04633ae72d3c-WVuZA; session_list=XN0YXJ0-8f0hd84c-4929-4782-8aa7-04633ae72d3c-WVuZA_XN0YXJ0-cbcu2232-bd07-4258-ba84-bd690f3f3b9a-WVuZA; passport_app_access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; sl_session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; swp_csrf_token=06b39a70-8d22-40fb-9d86-a39aecc2a1b6; t_beda37=23d411157af861afbc9c1bf6032b116898bb57702b61c0c8e7ac536df6d7ccec'
    fly_book_auth.perepare_auth(cookie_str)
    params = ParamsBuilder.build_receive_msg_param(fly_book_auth).get()
    url = f"wss://msg-frontier.feishu.cn/ws/v2?{urlencode(params)}"
    live.start_ws(url)